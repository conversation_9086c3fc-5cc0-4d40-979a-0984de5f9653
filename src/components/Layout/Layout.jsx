import Sidebar from "../Sidebar/Sidebar";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
const ROUTE_TO_NAV_MAP = {
  "/pending-accounts": "accounts",
  "/pending-invites": "users",
};

const Layout = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const currentView = ROUTE_TO_NAV_MAP[location.pathname] || 
    location.pathname.replace("/", "") || "dashboard";

  const onViewChange = (key) => {
    navigate(`/${key}`);
  };

  const onLogout = () => {
    localStorage.removeItem("accessToken");
    localStorage.removeItem("refreshToken");
    navigate("/login");
  };

  return (
    <div className="min-h-screen flex bg-gray-50">
      <Sidebar
        currentView={currentView}
        onViewChange={onViewChange}
        onLogout={onLogout}
      />
      <main className="ml-[250px] p-6 w-full overflow-auto">
        <Outlet />
      </main>
    </div>
  );
};

export default Layout;

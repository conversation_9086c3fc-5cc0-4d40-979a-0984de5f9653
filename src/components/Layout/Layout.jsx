import Sidebar from "../Sidebar/Sidebar";
import { Outlet, useLocation, useNavigate } from "react-router-dom";

const Layout = () => {
  const location = useLocation();
  const navigate = useNavigate();

  // Map pending routes to their parent navigation items
  const getNavigationKey = (pathname) => {
    if (pathname === "/pending-accounts") return "accounts";
    if (pathname === "/pending-invites") return "users";
    return pathname.replace("/", "") || "dashboard";
  };

  const currentView = getNavigationKey(location.pathname);

  const onViewChange = (key) => {
    navigate(`/${key}`);
  };

  const onLogout = () => {
    localStorage.removeItem("accessToken");
    localStorage.removeItem("refreshToken");
    navigate("/login");
  };

  return (
    <div className="min-h-screen flex bg-gray-50">
      <Sidebar
        currentView={currentView}
        onViewChange={onViewChange}
        onLogout={onLogout}
      />
      <main className="ml-[250px] p-6 w-full overflow-auto">
        <Outlet />
      </main>
    </div>
  );
};

export default Layout;
